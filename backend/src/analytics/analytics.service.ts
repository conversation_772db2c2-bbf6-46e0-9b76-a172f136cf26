import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { type Repository, Between, In } from "typeorm";
import { Application } from "src/applications/entities/application.entity";
import { Rating } from "src/ratings/entities/rating.entity";
import { Payout } from "src/payouts/entities/payout.entity";
import { Job } from "src/jobs/entities/job.entity";
import { User } from "src/users/entities/user.entity";
import { JobStatus } from "src/common/enums/job-status.enum";
import { ApplicationStatus } from "@shared/types";

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Job)
    private jobsRepository: Repository<Job>,
    @InjectRepository(Application)
    private applicationsRepository: Repository<Application>,
    @InjectRepository(Rating)
    private ratingsRepository: Repository<Rating>,
    @InjectRepository(Payout)
    private payoutsRepository: Repository<Payout>
  ) {}

  async getAdminDashboardStats() {
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));

    // Calculate date ranges for overview data
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 5);

    const [
      totalUsers,
      newUsersToday,
      activeJobs,
      newJobsToday,
      pendingDisputes,
      newDisputesToday,
      totalRevenue,
      revenueToday,
      recentJobs,
    ] = await Promise.all([
      this.usersRepository.count(),
      this.usersRepository.count({
        where: { createdAt: Between(startOfDay, new Date()) },
      }),
      this.jobsRepository.count({
        where: { status: JobStatus.OPEN },
      }),
      this.jobsRepository.count({
        where: {
          createdAt: Between(startOfDay, new Date()),
        },
      }),
      this.jobsRepository
        .createQueryBuilder("job")
        .innerJoin("job.disputes", "dispute")
        .where("dispute.status = :status", { status: "open" })
        .getCount(),
      this.jobsRepository
        .createQueryBuilder("job")
        .innerJoin("job.disputes", "dispute")
        .where("dispute.createdAt BETWEEN :start AND :end", {
          start: startOfDay,
          end: new Date(),
        })
        .getCount(),
      this.payoutsRepository.sum("commission"),
      this.payoutsRepository.sum("commission", {
        createdAt: Between(startOfDay, new Date()),
      }),
      this.jobsRepository.find({
        take: 5,
        order: { createdAt: "DESC" },
        relations: ["postedBy"],
      }),
    ]);

    // Generate real data for the overview chart
    const overviewData = await this.generateOverviewData(startDate, endDate);

    return {
      totalUsers,
      newUsersToday,
      activeJobs,
      newJobsToday,
      pendingDisputes,
      newDisputesToday,
      totalRevenue: totalRevenue || 0,
      revenueToday: revenueToday || 0,
      recentJobs,
      overviewData,
    };
  }

  async getCompanyPerformanceAnalytics(companyId: string, timeRange: string) {
    const endDate = new Date();
    const startDate = new Date();
    const previousPeriodEndDate = new Date(startDate);

    // Calculate start date based on time range
    switch (timeRange) {
      case "7days":
        startDate.setDate(endDate.getDate() - 7);
        previousPeriodEndDate.setDate(startDate.getDate() - 1);
        break;
      case "30days":
        startDate.setDate(endDate.getDate() - 30);
        previousPeriodEndDate.setDate(startDate.getDate() - 1);
        break;
      case "90days":
        startDate.setDate(endDate.getDate() - 90);
        previousPeriodEndDate.setDate(startDate.getDate() - 1);
        break;
      case "year":
        startDate.setDate(endDate.getDate() - 365);
        previousPeriodEndDate.setDate(startDate.getDate() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
        previousPeriodEndDate.setDate(startDate.getDate() - 1);
    }

    // Calculate previous period start date (same duration as current period)
    const previousPeriodStartDate = new Date(previousPeriodEndDate);
    previousPeriodStartDate.setDate(
      previousPeriodEndDate.getDate() -
        (endDate.getDate() - startDate.getDate())
    );

    // Get jobs posted by the company in the current time range
    const jobs = await this.jobsRepository.find({
      where: {
        companyId,
        createdAt: Between(startDate, endDate),
      },
      relations: ["applications", "applications.worker"],
    });

    // Get jobs from previous period for growth calculation
    const previousPeriodJobs = await this.jobsRepository.find({
      where: {
        companyId,
        createdAt: Between(previousPeriodStartDate, previousPeriodEndDate),
      },
      relations: ["applications", "applications.worker"],
    });

    // Get job IDs
    const jobIds = jobs.map((job) => job.id);

    // Get applications for these jobs
    const applications = await this.applicationsRepository.find({
      where: {
        jobId: In(jobIds),
      },
      relations: ["worker"],
    });

    // Get applications from previous period
    const previousPeriodApplications = await this.applicationsRepository.find({
      where: {
        jobId: In(previousPeriodJobs.map((job) => job.id)),
      },
      relations: ["worker"],
    });

    // Calculate metrics
    const totalJobs = jobs.length;
    const totalApplicants = applications.length;
    const completedJobs = jobs.filter(
      (job) => job.status === JobStatus.COMPLETED
    ).length;
    const completionRate =
      totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;

    // Calculate previous period metrics for growth
    const previousTotalJobs = previousPeriodJobs.length;
    const previousTotalApplicants = previousPeriodApplications.length;
    const previousCompletedJobs = previousPeriodJobs.filter(
      (job) => job.status === JobStatus.COMPLETED
    ).length;
    const previousCompletionRate =
      previousTotalJobs > 0
        ? (previousCompletedJobs / previousTotalJobs) * 100
        : 0;

    // Calculate growth percentages
    const jobsGrowth =
      previousTotalJobs > 0
        ? ((totalJobs - previousTotalJobs) / previousTotalJobs) * 100
        : 0;

    const applicantsGrowth =
      previousTotalApplicants > 0
        ? ((totalApplicants - previousTotalApplicants) /
            previousTotalApplicants) *
          100
        : 0;

    const completionRateGrowth =
      previousCompletionRate > 0
        ? ((completionRate - previousCompletionRate) / previousCompletionRate) *
          100
        : 0;

    // Calculate average trust score of applicants
    const applicantTrustScores = applications.map(
      (app) => app.worker.trustScore
    );
    const avgTrustScore =
      applicantTrustScores.length > 0
        ? applicantTrustScores.reduce((sum, score) => sum + score, 0) /
          applicantTrustScores.length
        : 0;

    // Calculate previous period trust score
    const previousApplicantTrustScores = previousPeriodApplications.map(
      (app) => app.worker.trustScore
    );
    const previousAvgTrustScore =
      previousApplicantTrustScores.length > 0
        ? previousApplicantTrustScores.reduce((sum, score) => sum + score, 0) /
          previousApplicantTrustScores.length
        : 0;

    const trustScoreGrowth =
      previousAvgTrustScore > 0
        ? ((avgTrustScore - previousAvgTrustScore) / previousAvgTrustScore) *
          100
        : 0;

    // Generate performance trends (real data)
    const performanceTrends = await this.generatePerformanceTrends(
      companyId,
      startDate,
      endDate
    );

    // Generate trust score distribution (real data)
    const trustScoreDistribution =
      this.generateTrustScoreDistribution(applicantTrustScores);

    // Generate job completion data (real data)
    const jobCompletionData = await this.generateJobCompletionData(
      companyId,
      startDate,
      endDate
    );

    // Generate top performers (real data)
    const topPerformers = await this.generateTopPerformers(
      companyId,
      startDate,
      endDate
    );

    return {
      totalJobs,
      totalApplicants,
      completionRate,
      avgTrustScore,
      jobsGrowth,
      applicantsGrowth,
      completionRateGrowth,
      trustScoreGrowth,
      performanceTrends,
      trustScoreDistribution,
      jobCompletionData,
      topPerformers,
    };
  }

  // Helper methods to generate real data for charts
  /**
   * Generate overview data from real data
   */
  private async generateOverviewData(startDate: Date, endDate: Date) {
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    const result = [];

    // Create a date for the first day of each month in the range
    let currentDate = new Date(startDate);
    currentDate.setDate(1); // Set to first day of month

    while (currentDate <= endDate) {
      const monthIndex = currentDate.getMonth();
      const monthName = months[monthIndex];
      const year = currentDate.getFullYear();

      // Calculate end of month
      const nextMonth = new Date(currentDate);
      nextMonth.setMonth(nextMonth.getMonth() + 1);

      // Get jobs created in this month
      const jobsCount = await this.jobsRepository.count({
        where: {
          createdAt: Between(currentDate, nextMonth),
        },
      });

      // Get applications created in this month
      const applicationsCount = await this.applicationsRepository.count({
        where: {
          createdAt: Between(currentDate, nextMonth),
        },
      });

      // Get jobs completed in this month
      const completionsCount = await this.jobsRepository.count({
        where: {
          status: JobStatus.COMPLETED,
          updatedAt: Between(currentDate, nextMonth),
        },
      });

      result.push({
        name: `${monthName} ${year !== new Date().getFullYear() ? year : ""}`,
        jobs: jobsCount,
        applications: applicationsCount,
        completions: completionsCount,
      });

      // Move to next month
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return result;
  }

  /**
   * Generate real performance trends data
   */
  private async generatePerformanceTrends(
    companyId: string,
    startDate: Date,
    endDate: Date
  ) {
    const days = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const data = [];

    // For each day in the range
    for (let i = 0; i < days; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(currentDate.getDate() + i);

      const nextDate = new Date(currentDate);
      nextDate.setDate(nextDate.getDate() + 1);

      // Get jobs created on this day
      const jobsCount = await this.jobsRepository.count({
        where: {
          companyId,
          createdAt: Between(currentDate, nextDate),
        },
      });

      // Get applications received on this day
      const applicationsCount = await this.applicationsRepository.count({
        where: {
          job: {
            companyId,
          },
          createdAt: Between(currentDate, nextDate),
        },
      });

      // Get jobs completed on this day
      const completionsCount = await this.jobsRepository.count({
        where: {
          companyId,
          status: JobStatus.COMPLETED,
          updatedAt: Between(currentDate, nextDate),
        },
      });

      data.push({
        date: currentDate.toISOString().split("T")[0],
        jobs: jobsCount,
        applications: applicationsCount,
        completions: completionsCount,
      });
    }

    return data;
  }

  /**
   * Generate trust score distribution from real data
   */
  private generateTrustScoreDistribution(scores: number[]) {
    const distribution = [
      { range: "0-20", count: 0 },
      { range: "21-40", count: 0 },
      { range: "41-60", count: 0 },
      { range: "61-80", count: 0 },
      { range: "81-100", count: 0 },
    ];

    scores.forEach((score) => {
      if (score <= 20) distribution[0].count++;
      else if (score <= 40) distribution[1].count++;
      else if (score <= 60) distribution[2].count++;
      else if (score <= 80) distribution[3].count++;
      else distribution[4].count++;
    });

    return distribution;
  }

  /**
   * Generate job completion data from real data
   */
  private async generateJobCompletionData(
    companyId: string,
    startDate: Date,
    endDate: Date
  ) {
    // Get all jobs for the company in the time range
    const jobs = await this.jobsRepository.find({
      where: {
        companyId,
        createdAt: Between(startDate, endDate),
      },
    });

    // Count jobs by status
    const completedCount = jobs.filter(
      (job) => job.status === JobStatus.COMPLETED
    ).length;
    const cancelledCount = jobs.filter(
      (job) => job.status === JobStatus.CANCELLED
    ).length;

    // Count no-shows (jobs where worker didn't show up)
    // Since JobStatus.FAILED doesn't exist, we'll count cancelled jobs with a specific condition
    // This is a placeholder - you may need to adjust based on your business logic
    const noShowCount = jobs.filter(
      (job) =>
        job.status === JobStatus.CANCELLED && job.cancelledReason === "no_show"
    ).length;

    // Count disputed jobs
    const disputedCount = await this.jobsRepository
      .createQueryBuilder("job")
      .innerJoin("job.disputes", "dispute")
      .where("job.companyId = :companyId", { companyId })
      .andWhere("job.createdAt BETWEEN :start AND :end", {
        start: startDate,
        end: endDate,
      })
      .getCount();

    return [
      { name: "Completed", value: completedCount },
      { name: "Cancelled", value: cancelledCount },
      { name: "No-show", value: noShowCount },
      { name: "Disputed", value: disputedCount },
    ];
  }

  /**
   * Generate top performers from real data
   */
  private async generateTopPerformers(
    companyId: string,
    startDate: Date,
    endDate: Date
  ) {
    // Get all completed jobs for the company in the time range
    const completedJobs = await this.jobsRepository.find({
      where: {
        companyId,
        status: JobStatus.COMPLETED,
        updatedAt: Between(startDate, endDate),
      },
      relations: ["applications", "applications.worker"],
    });

    // Get all workers who completed jobs
    const workerMap = new Map();

    for (const job of completedJobs) {
      const hiredApplication = job.applications.find(
        (app) => app.status === ApplicationStatus.ACCEPTED
      );

      if (hiredApplication && hiredApplication.worker) {
        const workerId = hiredApplication.worker.id;

        if (!workerMap.has(workerId)) {
          workerMap.set(workerId, {
            id: workerId,
            name: hiredApplication.worker.name,
            completedJobs: 0,
            totalRating: 0,
            ratingCount: 0,
            trustScore: hiredApplication.worker.trustScore,
          });
        }

        const workerData = workerMap.get(workerId);
        workerData.completedJobs++;

        // Get rating for this job if exists
        const rating = await this.ratingsRepository.findOne({
          where: {
            jobId: job.id,
            ratedUserId: workerId, // Using ratedUserId instead of workerId
          },
        });

        if (rating) {
          workerData.totalRating += rating.stars; // Using stars instead of rating
          workerData.ratingCount++;
        }
      }
    }

    // Convert map to array and calculate average ratings
    const topPerformers = Array.from(workerMap.values())
      .map((worker) => ({
        id: worker.id,
        name: worker.name,
        completedJobs: worker.completedJobs,
        avgRating:
          worker.ratingCount > 0 ? worker.totalRating / worker.ratingCount : 0,
        trustScore: worker.trustScore,
      }))
      .sort((a, b) => b.completedJobs - a.completedJobs)
      .slice(0, 5); // Get top 5

    return topPerformers;
  }
}

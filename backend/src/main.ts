import { NestFactory } from "@nestjs/core";
import { ValidationPipe, VersioningType, Logger } from "@nestjs/common";
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger";
import { ConfigService } from "@nestjs/config";
import { AppModule } from "./app.module";
import { HttpExceptionFilter } from "./common/filters/http-exception.filter";
import { AllExceptionsFilter } from "./common/filters/all-exceptions.filter";
import { TransformInterceptor } from "./common/interceptors/transform.interceptor";
import helmet from "helmet";
import * as compression from "compression";

async function bootstrap() {
  const logger = new Logger("Bootstrap");

  try {
    const app = await NestFactory.create(AppModule, {
      logger: ["error", "warn", "log", "debug", "verbose"],
    });

    const configService = app.get(ConfigService);
    const nodeEnv = configService.get<string>("NODE_ENV", "development");
    const isProduction = nodeEnv === "production";

    // Enhanced CORS configuration
    const corsOrigins = configService.get<string>("CORS_ORIGIN", "");
    const allowedOrigins = corsOrigins
      ? corsOrigins.split(",").filter(Boolean)
      : [
          "http://localhost:3000", // Company portal
          "http://localhost:3001", // Admin portal
          "http://localhost:3002", // Admin portal alternative
          "http://localhost:19006", // Expo web
        ];

    app.enableCors({
      origin: isProduction ? allowedOrigins : true,
      credentials: true,
      methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
      allowedHeaders: [
        "Content-Type",
        "Authorization",
        "Accept",
        "X-Requested-With",
      ],
      maxAge: 86400, // 24 hours
    });

    // Enhanced security middleware
    app.use(
      helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
          },
        },
        crossOriginEmbedderPolicy: false,
        hsts: {
          maxAge: 31536000,
          includeSubDomains: true,
          preload: true,
        },
      })
    );

    // Compression
    app.use(compression());

    // API versioning
    app.enableVersioning({
      type: VersioningType.URI,
      prefix: "api/v",
      defaultVersion: "1",
    });

    // Enhanced global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: isProduction, // Strict in production
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
        validateCustomDecorators: true,
        disableErrorMessages: isProduction,
        validationError: {
          target: false,
          value: false,
        },
      })
    );

    // Global exception filters
    app.useGlobalFilters(new AllExceptionsFilter(), new HttpExceptionFilter());

    // Global interceptors
    app.useGlobalInterceptors(new TransformInterceptor());

    // Swagger documentation (only in non-production)
    if (!isProduction) {
      const config = new DocumentBuilder()
        .setTitle("Job Platform API")
        .setDescription(
          "API documentation for the Inventory & Stock Audit Job Platform"
        )
        .setVersion("1.0")
        .addBearerAuth()
        .addTag("auth", "Authentication endpoints")
        .addTag("users", "User management endpoints")
        .addTag("jobs", "Job management endpoints")
        .addTag("applications", "Job application endpoints")
        .addTag("ratings", "Rating management endpoints")
        .addTag("payouts", "Payout management endpoints")
        .addTag("payments", "Payment processing endpoints")
        .addTag("disputes", "Dispute management endpoints")
        .addTag("notifications", "Notification management endpoints")
        .addTag("chat", "Chat messaging endpoints")
        .addTag("gamification", "Gamification and badges endpoints")
        .addTag("analytics", "Analytics and reporting endpoints")
        .addTag("otp", "OTP verification endpoints")
        .addTag("job-templates", "Job template management endpoints")
        .addTag("settings", "System settings endpoints")
        .addTag("reports", "Report generation endpoints")
        .addTag("activity-logs", "Activity logging endpoints")
        .addTag("favorites", "Favorites management endpoints")
        .addTag("documents", "Document management endpoints")
        .addTag("health", "Health check endpoints")
        .build();

      const document = SwaggerModule.createDocument(app, config);
      const swaggerDocRoute = "api/docs";
      SwaggerModule.setup(swaggerDocRoute, app, document);

      logger.log(
        `Swagger documentation available at: http://localhost:${configService.get<number>(
          "PORT",
          3000
        )}/${swaggerDocRoute}`
      );
    }

    // Graceful shutdown handlers
    process.on("SIGTERM", async () => {
      logger.log("SIGTERM received, shutting down gracefully");
      await app.close();
      process.exit(0);
    });

    process.on("SIGINT", async () => {
      logger.log("SIGINT received, shutting down gracefully");
      await app.close();
      process.exit(0);
    });

    // Start the server
    const port = configService.get<number>("PORT", 3000);
    await app.listen(port, "0.0.0.0");

    logger.log(`Application is running on: http://localhost:${port}`);
    logger.log(`Environment: ${nodeEnv}`);
    logger.log(`API Documentation: ${!isProduction ? "Enabled" : "Disabled"}`);
  } catch (error) {
    logger.error("Failed to start application", error);
    process.exit(1);
  }
}

bootstrap();

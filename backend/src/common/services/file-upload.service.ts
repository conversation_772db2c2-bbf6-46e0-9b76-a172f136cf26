import { Injectable, BadRequestException, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { SanitizationService } from "./sanitization.service";
import * as path from "path";
import * as fs from "fs/promises";
import * as crypto from "crypto";

export interface FileUploadOptions {
  allowedMimeTypes?: string[];
  maxFileSize?: number; // in bytes
  allowedExtensions?: string[];
  generateUniqueFilename?: boolean;
  virusScan?: boolean;
}

export interface UploadedFile {
  originalName: string;
  filename: string;
  path: string;
  size: number;
  mimeType: string;
  extension: string;
  hash: string;
}

@Injectable()
export class FileUploadService {
  private readonly logger = new Logger(FileUploadService.name);
  private readonly uploadDir: string;
  private readonly maxFileSize: number;
  private readonly allowedMimeTypes: string[];
  private readonly allowedExtensions: string[];

  constructor(
    private configService: ConfigService,
    private sanitizationService: SanitizationService
  ) {
    this.uploadDir = this.configService.get<string>("UPLOAD_DIR", "./uploads");
    this.maxFileSize = this.configService.get<number>(
      "MAX_FILE_SIZE",
      10 * 1024 * 1024
    ); // 10MB
    this.allowedMimeTypes = this.configService
      .get<string>(
        "ALLOWED_MIME_TYPES",
        "image/jpeg,image/png,image/gif,application/pdf,text/plain"
      )
      .split(",");
    this.allowedExtensions = this.configService
      .get<string>("ALLOWED_EXTENSIONS", ".jpg,.jpeg,.png,.gif,.pdf,.txt")
      .split(",");

    this.ensureUploadDirectory();
  }

  private async ensureUploadDirectory(): Promise<void> {
    try {
      await fs.access(this.uploadDir);
    } catch {
      await fs.mkdir(this.uploadDir, { recursive: true });
      this.logger.log(`Created upload directory: ${this.uploadDir}`);
    }
  }

  async uploadFile(
    file: Express.Multer.File,
    options: FileUploadOptions = {}
  ): Promise<UploadedFile> {
    // Validate file
    this.validateFile(file, options);

    // Sanitize filename
    const sanitizedName = this.sanitizationService.sanitizeFileName(
      file.originalname
    );

    // Generate unique filename if requested
    const filename = options.generateUniqueFilename
      ? this.generateUniqueFilename(sanitizedName)
      : sanitizedName;

    // Create file path
    const filePath = path.join(this.uploadDir, filename);

    // Calculate file hash for integrity
    const hash = this.calculateFileHash(file.buffer);

    // Check for duplicate files
    const existingFile = await this.findFileByHash(hash);
    if (existingFile) {
      this.logger.log(
        `File already exists with hash ${hash}, returning existing file`
      );
      return existingFile;
    }

    // Perform virus scan if enabled
    if (options.virusScan) {
      await this.performVirusScan(file.buffer);
    }

    // Save file
    await fs.writeFile(filePath, file.buffer);

    const uploadedFile: UploadedFile = {
      originalName: file.originalname,
      filename,
      path: filePath,
      size: file.size,
      mimeType: file.mimetype,
      extension: path.extname(filename),
      hash,
    };

    this.logger.log(
      `File uploaded successfully: ${filename} (${file.size} bytes)`
    );
    return uploadedFile;
  }

  private validateFile(
    file: Express.Multer.File,
    options: FileUploadOptions
  ): void {
    if (!file) {
      throw new BadRequestException("No file provided");
    }

    // Check file size
    const maxSize = options.maxFileSize || this.maxFileSize;
    if (file.size > maxSize) {
      throw new BadRequestException(
        `File size exceeds maximum allowed size of ${maxSize} bytes`
      );
    }

    // Check MIME type
    const allowedMimes = options.allowedMimeTypes || this.allowedMimeTypes;
    if (!allowedMimes.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type ${file.mimetype} is not allowed`
      );
    }

    // Check file extension
    const extension = path.extname(file.originalname).toLowerCase();
    const allowedExts = options.allowedExtensions || this.allowedExtensions;
    if (!allowedExts.includes(extension)) {
      throw new BadRequestException(
        `File extension ${extension} is not allowed`
      );
    }

    // Check for suspicious file names
    if (this.isSuspiciousFilename(file.originalname)) {
      throw new BadRequestException("Suspicious filename detected");
    }

    // Validate file content matches extension
    if (!this.validateFileContent(file.buffer, file.mimetype)) {
      throw new BadRequestException(
        "File content does not match declared type"
      );
    }
  }

  private isSuspiciousFilename(filename: string): boolean {
    const suspiciousPatterns = [
      /\.(exe|bat|cmd|scr|pif|com|vbs|js|jar|app|deb|rpm)$/i,
      /\.\w+\.(exe|bat|cmd|scr|pif|com|vbs|js)$/i, // Double extensions
      /%[0-9a-f]{2}/i, // URL encoded characters
      /[<>:"|?*]/, // Invalid filename characters
    ];

    return suspiciousPatterns.some((pattern) => pattern.test(filename));
  }

  private validateFileContent(buffer: Buffer, mimeType: string): boolean {
    // Basic file signature validation
    const signatures: Record<string, Buffer[]> = {
      "image/jpeg": [Buffer.from([0xff, 0xd8, 0xff])],
      "image/png": [Buffer.from([0x89, 0x50, 0x4e, 0x47])],
      "image/gif": [Buffer.from([0x47, 0x49, 0x46, 0x38])],
      "application/pdf": [Buffer.from([0x25, 0x50, 0x44, 0x46])],
    };

    const expectedSignatures = signatures[mimeType];
    if (!expectedSignatures) {
      return true; // Skip validation for unknown types
    }

    return expectedSignatures.some((signature) =>
      buffer.subarray(0, signature.length).equals(signature)
    );
  }

  private generateUniqueFilename(originalName: string): string {
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension);
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString("hex");

    return `${baseName}_${timestamp}_${random}${extension}`;
  }

  private calculateFileHash(buffer: Buffer): string {
    return crypto.createHash("sha256").update(buffer).digest("hex");
  }

  private async findFileByHash(_hash: string): Promise<UploadedFile | null> {
    // This would typically query a database
    // For now, we'll return null to indicate no duplicate found
    return null;
  }

  private async performVirusScan(buffer: Buffer): Promise<void> {
    /**
     * PLACEHOLDER: Virus Scanning Implementation
     *
     * This is a basic placeholder implementation for virus scanning.
     * For production use, integrate with a proper antivirus solution:
     *
     * Recommended Options:
     * 1. ClamAV (Open source) - Use node-clamav package
     * 2. VirusTotal API - For cloud-based scanning
     * 3. Windows Defender API - For Windows environments
     * 4. Commercial solutions like Symantec, McAfee APIs
     *
     * Implementation steps:
     * 1. Install and configure antivirus engine
     * 2. Replace this method with actual scanning logic
     * 3. Handle scan results and quarantine infected files
     * 4. Add proper error handling and logging
     * 5. Consider async scanning for large files
     */
    this.logger.debug(
      "Virus scan placeholder - implement with actual scanner in production"
    );

    // Simple check for suspicious patterns
    const suspiciousPatterns = [
      Buffer.from("EICAR-STANDARD-ANTIVIRUS-TEST-FILE"),
      Buffer.from("X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR"),
    ];

    for (const pattern of suspiciousPatterns) {
      if (buffer.includes(pattern)) {
        throw new BadRequestException("File contains suspicious content");
      }
    }
  }

  async deleteFile(filename: string): Promise<boolean> {
    try {
      const filePath = path.join(this.uploadDir, filename);
      await fs.unlink(filePath);
      this.logger.log(`File deleted successfully: ${filename}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete file ${filename}:`, error);
      return false;
    }
  }

  async getFileInfo(filename: string): Promise<UploadedFile | null> {
    try {
      const filePath = path.join(this.uploadDir, filename);
      const stats = await fs.stat(filePath);
      const buffer = await fs.readFile(filePath);

      return {
        originalName: filename,
        filename,
        path: filePath,
        size: stats.size,
        mimeType: "application/octet-stream", // Would need mime detection
        extension: path.extname(filename),
        hash: this.calculateFileHash(buffer),
      };
    } catch (error) {
      this.logger.error(`Failed to get file info for ${filename}:`, error);
      return null;
    }
  }

  getFileUrl(filename: string): string {
    const baseUrl = this.configService.get<string>(
      "BASE_URL",
      "http://localhost:3000"
    );
    return `${baseUrl}/uploads/${filename}`;
  }
}

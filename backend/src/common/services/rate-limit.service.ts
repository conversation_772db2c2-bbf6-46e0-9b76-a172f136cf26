import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { CustomLoggerService } from "./logger.service";

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: any) => string;
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: Date;
  totalHits: number;
}

@Injectable()
export class RateLimitService {
  private readonly logger = new Logger(RateLimitService.name);
  private readonly store = new Map<
    string,
    { count: number; resetTime: number }
  >();
  private readonly configs = new Map<string, RateLimitConfig>();

  constructor(
    private configService: ConfigService,
    private customLogger: CustomLoggerService
  ) {
    this.setupDefaultConfigs();
    this.startCleanupInterval();
  }

  private setupDefaultConfigs(): void {
    // Global rate limit
    this.configs.set("global", {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
    });

    // API rate limit
    this.configs.set("api", {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60,
    });

    // Authentication rate limit
    this.configs.set("auth", {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5,
      skipSuccessfulRequests: true,
    });

    // Password reset rate limit
    this.configs.set("password-reset", {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3,
    });

    // File upload rate limit
    this.configs.set("upload", {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10,
    });

    // Job creation rate limit
    this.configs.set("job-creation", {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 20,
    });

    // Application submission rate limit
    this.configs.set("application", {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 5,
    });
  }

  private startCleanupInterval(): void {
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, data] of this.store.entries()) {
      if (data.resetTime <= now) {
        this.store.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(
        `Cleaned up ${cleanedCount} expired rate limit entries`
      );
    }
  }

  checkRateLimit(
    identifier: string,
    configName: string = "global",
    customConfig?: Partial<RateLimitConfig>
  ): { allowed: boolean; info: RateLimitInfo } {
    const baseConfig = this.configs.get(configName);

    if (!baseConfig) {
      this.logger.warn(
        `Rate limit config '${configName}' not found, using global config`
      );
      return this.checkRateLimit(identifier, "global");
    }

    const config = { ...baseConfig, ...customConfig };

    const key = `${configName}:${identifier}`;
    const now = Date.now();

    // Get or create entry
    let entry = this.store.get(key);

    if (!entry || entry.resetTime <= now) {
      // Create new window
      entry = {
        count: 0,
        resetTime: now + config.windowMs,
      };
      this.store.set(key, entry);
    }

    // Increment counter
    entry.count++;

    const info: RateLimitInfo = {
      limit: config.maxRequests,
      remaining: Math.max(0, config.maxRequests - entry.count),
      resetTime: new Date(entry.resetTime),
      totalHits: entry.count,
    };

    const allowed = entry.count <= config.maxRequests;

    if (!allowed) {
      this.customLogger.logSecurityEvent(
        `Rate limit exceeded for ${configName}`,
        "medium",
        {
          identifier,
          configName,
          count: entry.count,
          limit: config.maxRequests,
          resetTime: entry.resetTime,
        }
      );
    }

    return { allowed, info };
  }

  async isRateLimited(
    identifier: string,
    configName: string = "global",
    customConfig?: Partial<RateLimitConfig>
  ): Promise<boolean> {
    const result = this.checkRateLimit(identifier, configName, customConfig);
    return !result.allowed;
  }

  getRateLimitInfo(
    identifier: string,
    configName: string = "global"
  ): RateLimitInfo | null {
    const config = this.configs.get(configName);
    if (!config) return null;

    const key = `${configName}:${identifier}`;
    const entry = this.store.get(key);

    if (!entry) {
      return {
        limit: config.maxRequests,
        remaining: config.maxRequests,
        resetTime: new Date(Date.now() + config.windowMs),
        totalHits: 0,
      };
    }

    return {
      limit: config.maxRequests,
      remaining: Math.max(0, config.maxRequests - entry.count),
      resetTime: new Date(entry.resetTime),
      totalHits: entry.count,
    };
  }

  resetRateLimit(identifier: string, configName: string = "global"): void {
    const key = `${configName}:${identifier}`;
    this.store.delete(key);

    this.logger.log(`Rate limit reset for ${configName}:${identifier}`);
  }

  addConfig(name: string, config: RateLimitConfig): void {
    this.configs.set(name, config);
    this.logger.log(`Added rate limit config: ${name}`);
  }

  removeConfig(name: string): boolean {
    const removed = this.configs.delete(name);
    if (removed) {
      this.logger.log(`Removed rate limit config: ${name}`);
    }
    return removed;
  }

  getConfig(name: string): RateLimitConfig | undefined {
    return this.configs.get(name);
  }

  getAllConfigs(): Map<string, RateLimitConfig> {
    return new Map(this.configs);
  }

  getStats(): {
    totalEntries: number;
    configs: string[];
    memoryUsage: number;
  } {
    return {
      totalEntries: this.store.size,
      configs: Array.from(this.configs.keys()),
      memoryUsage: JSON.stringify(Array.from(this.store.entries())).length,
    };
  }

  // Helper method to generate keys based on request
  generateKey(req: any, prefix: string = ""): string {
    const ip = this.getClientIp(req);
    const userId = req.user?.id;

    if (userId) {
      return `${prefix}user:${userId}`;
    }

    return `${prefix}ip:${ip}`;
  }

  private getClientIp(req: any): string {
    return (
      req.headers["x-forwarded-for"]?.split(",")[0] ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      "unknown"
    );
  }

  // Middleware factory for Express
  createMiddleware(
    configName: string = "global",
    customConfig?: Partial<RateLimitConfig>
  ) {
    return (req: any, res: any, next: any) => {
      const identifier = this.generateKey(req);
      const result = this.checkRateLimit(identifier, configName, customConfig);

      // Set rate limit headers
      res.setHeader("X-RateLimit-Limit", result.info.limit);
      res.setHeader("X-RateLimit-Remaining", result.info.remaining);
      res.setHeader(
        "X-RateLimit-Reset",
        Math.ceil(result.info.resetTime.getTime() / 1000)
      );

      if (!result.allowed) {
        res.status(429).json({
          success: false,
          error: "Too Many Requests",
          message: "Rate limit exceeded. Please try again later.",
          retryAfter: Math.ceil(
            (result.info.resetTime.getTime() - Date.now()) / 1000
          ),
        });
        return;
      }

      next();
    };
  }
}

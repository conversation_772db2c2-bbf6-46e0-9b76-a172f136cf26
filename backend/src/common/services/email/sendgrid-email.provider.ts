import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as nodemailer from "nodemailer";
import nodemailerSendgrid from "nodemailer-sendgrid";
import { EmailOptions, EmailProvider } from "../../interfaces/email.interface";
import { BaseEmailProvider } from "./base-email.provider";

@Injectable()
export class SendgridEmailProvider extends BaseEmailProvider {
  private transporter!: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    super("SendGrid");
    this.initializeTransporter();
  }

  private initializeTransporter(): void {
    const apiKey = this.configService.get<string>("SENDGRID_API_KEY");

    if (!apiKey) {
      this.logger.warn(
        "SendGrid API key is missing. Email sending will be disabled."
      );
      return;
    }

    try {
      this.transporter = nodemailer.createTransport(
        nodemailerSendgrid({
          apiKey,
        })
      );

      this.logger.log("SendGrid transporter initialized successfully");
    } catch (error) {
      this.logger.error("Failed to initialize SendGrid transporter:", error);
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn(
        "SendGrid transporter not initialized. Email will not be sent."
      );
      return false;
    }

    try {
      const defaultFrom = this.configService.get<string>("EMAIL_FROM");
      const defaultReplyTo = this.configService.get<string>(
        "EMAIL_DEFAULT_REPLY_TO"
      );

      const mailOptions = {
        from: options.from || defaultFrom,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
        replyTo: options.replyTo || defaultReplyTo,
        attachments: options.attachments,
      };

      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully to ${options.to}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email to ${options.to}:`, error);
      return false;
    }
  }

  getProviderName(): string {
    return EmailProvider.SENDGRID;
  }
}

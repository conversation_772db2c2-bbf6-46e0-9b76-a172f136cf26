import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { GenericChat } from "./entities/generic-chat.entity";
import { ChatParticipant } from "./entities/chat-participant.entity";
import { ChatMessage } from "./entities/chat-message.entity";
import { CreateGenericChatDto } from "./dto/create-generic-chat.dto";
import { CreateGenericMessageDto } from "./dto/create-generic-message.dto";
import { CreateWorkerCompanyChatDto } from "./dto/create-worker-company-chat.dto";
import {
  ChatType,
  ParticipantRole,
  MessageStatus,
} from "./enums/chat-type.enum";
import { PaginatedApiResponse } from "../common/interfaces/api-response.interface";

@Injectable()
export class GenericChatService {
  constructor(
    @InjectRepository(GenericChat)
    private readonly genericChatRepository: Repository<GenericChat>,
    @InjectRepository(ChatParticipant)
    private readonly chatParticipantRepository: Repository<ChatParticipant>,
    @InjectRepository(ChatMessage)
    private readonly chatMessageRepository: Repository<ChatMessage>
  ) {}

  /**
   * Create a new generic chat
   */
  async createGenericChat(
    creatorId: string,
    createChatDto: CreateGenericChatDto
  ): Promise<GenericChat> {
    const { participants, ...chatData } = createChatDto;

    // Validate that creator is included in participants
    const creatorParticipant = participants.find((p) => p.userId === creatorId);
    if (!creatorParticipant) {
      throw new BadRequestException("Creator must be included in participants");
    }

    // Create the chat
    const chat = this.genericChatRepository.create({
      ...chatData,
      createdBy: creatorId,
      lastMessageAt: new Date(),
    });

    const savedChat = await this.genericChatRepository.save(chat);

    // Create participants
    const chatParticipants = participants.map((p) =>
      this.chatParticipantRepository.create({
        chatId: savedChat.id,
        userId: p.userId,
        role: p.role,
        joinedAt: new Date(),
        isActive: true,
      })
    );

    await this.chatParticipantRepository.save(chatParticipants);

    // Return chat with participants
    const result = await this.findGenericChatById(savedChat.id, creatorId);
    if (!result) {
      throw new Error("Failed to retrieve created chat");
    }
    return result;
  }

  /**
   * Create worker-company chat (backward compatibility)
   */
  async createWorkerCompanyChat(
    creatorId: string,
    createChatDto: CreateWorkerCompanyChatDto
  ): Promise<GenericChat> {
    const { workerId, companyId, jobId } = createChatDto;

    // Validate that creator is either worker or company
    if (creatorId !== workerId && creatorId !== companyId) {
      throw new BadRequestException(
        "You can only create chats where you are either the worker or company"
      );
    }

    // Check if chat already exists
    const existingChat = await this.findWorkerCompanyChat(
      workerId,
      companyId,
      jobId
    );
    if (existingChat) {
      return existingChat;
    }

    // Convert to generic chat format
    const genericChatDto: CreateGenericChatDto = {
      type: ChatType.WORKER_COMPANY,
      contextId: jobId || "",
      contextType: jobId ? "job" : "",
      participants: [
        { userId: workerId, role: ParticipantRole.WORKER },
        { userId: companyId, role: ParticipantRole.COMPANY },
      ],
      metadata: { legacy: true },
    };

    return this.createGenericChat(creatorId, genericChatDto);
  }

  /**
   * Find existing worker-company chat
   */
  private async findWorkerCompanyChat(
    workerId: string,
    companyId: string,
    jobId?: string
  ): Promise<GenericChat | null> {
    const query = this.genericChatRepository
      .createQueryBuilder("chat")
      .leftJoinAndSelect("chat.participants", "participant")
      .leftJoinAndSelect("participant.user", "user")
      .where("chat.type = :type", { type: ChatType.WORKER_COMPANY });

    if (jobId) {
      query.andWhere("chat.contextId = :jobId", { jobId });
    }

    const chats = await query.getMany();

    // Find chat with exact participants
    return (
      chats.find((chat) => {
        const participantIds = chat.participants.map((p) => p.userId);
        return (
          participantIds.includes(workerId) &&
          participantIds.includes(companyId)
        );
      }) || null
    );
  }

  /**
   * Find chat by ID with access control
   */
  async findGenericChatById(
    chatId: string,
    userId: string
  ): Promise<GenericChat | null> {
    const chat = await this.genericChatRepository.findOne({
      where: { id: chatId },
      relations: ["participants", "participants.user"],
    });

    if (!chat) {
      return null;
    }

    // Check if user is a participant
    const isParticipant = chat.participants.some((p) => p.userId === userId);
    if (!isParticipant) {
      throw new ForbiddenException("You are not a participant in this chat");
    }

    return chat;
  }

  /**
   * Add message to generic chat
   */
  async addGenericMessage(
    chatId: string,
    senderId: string,
    createMessageDto: CreateGenericMessageDto
  ): Promise<ChatMessage> {
    const chat = await this.findGenericChatById(chatId, senderId);
    if (!chat) {
      throw new NotFoundException(`Chat with ID ${chatId} not found`);
    }

    // Create message
    const message = this.chatMessageRepository.create({
      genericChatId: chatId,
      senderId,
      ...createMessageDto,
      status: MessageStatus.SENT,
    });

    const savedMessage = await this.chatMessageRepository.save(message);

    // Update unread counts for other participants
    await this.updateUnreadCounts(chatId, senderId);

    // Update chat's last message timestamp
    chat.lastMessageAt = new Date();
    await this.genericChatRepository.save(chat);

    return savedMessage;
  }

  /**
   * Update unread counts for participants
   */
  private async updateUnreadCounts(
    chatId: string,
    senderId: string
  ): Promise<void> {
    await this.chatParticipantRepository
      .createQueryBuilder()
      .update(ChatParticipant)
      .set({ unreadCount: () => "unreadCount + 1" })
      .where("chatId = :chatId", { chatId })
      .andWhere("userId != :senderId", { senderId })
      .andWhere("isActive = true")
      .execute();
  }

  /**
   * Mark messages as read for a participant
   */
  async markAsRead(chatId: string, userId: string): Promise<void> {
    const chat = await this.findGenericChatById(chatId, userId);
    if (!chat) {
      throw new NotFoundException(`Chat with ID ${chatId} not found`);
    }

    // Reset unread count for the user
    await this.chatParticipantRepository.update(
      { chatId, userId },
      { unreadCount: 0, lastReadAt: new Date() }
    );

    // Mark messages as read
    await this.chatMessageRepository
      .createQueryBuilder()
      .update(ChatMessage)
      .set({ isRead: true, readAt: new Date() })
      .where("genericChatId = :chatId", { chatId })
      .andWhere("senderId != :userId", { userId })
      .andWhere("isRead = false")
      .execute();
  }

  /**
   * Get chats for a user
   */
  async findChatsForUser(
    userId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedApiResponse<GenericChat>> {
    const skip = (page - 1) * limit;

    const [chats, total] = await this.genericChatRepository
      .createQueryBuilder("chat")
      .leftJoinAndSelect("chat.participants", "participant")
      .leftJoinAndSelect("participant.user", "user")
      .where("participant.userId = :userId", { userId })
      .andWhere("participant.isActive = true")
      .orderBy("chat.lastMessageAt", "DESC")
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: chats,
      message: "Chats retrieved successfully",
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
}

import { Injectable, NotFoundException, Logger, Inject } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Between } from "typeorm";
import { Document } from "../users/entities/document.entity";
import { User } from "../users/entities/user.entity";
import { DocumentsService } from "../documents/documents.service";
import { NotificationsService } from "../notifications/notifications.service";
import { ActivityLogService } from "../activity-log/activity-log.service";
import { VerificationStatus } from "../common/enums/verification-status.enum";
import { DocumentType } from "../common/enums/document-type.enum";
import { UserRole } from "@shared/types/user";

export interface KycDashboardStats {
  totalPendingDocuments: number;
  totalVerifiedDocuments: number;
  totalRejectedDocuments: number;
  pendingWorkerKyc: number;
  pendingCompanyKyc: number;
  verifiedWorkers: number;
  verifiedCompanies: number;
  recentSubmissions: number; // Last 24 hours
  averageVerificationTime: number; // In hours
}

export interface PendingKycItem {
  id: string;
  userId: string;
  userType: "worker" | "company";
  userName: string;
  userEmail: string;
  documentType: DocumentType;
  documentUrl: string;
  documentNumber?: string;
  submittedAt: Date;
  priority: "high" | "medium" | "low";
}

export interface KycVerificationHistory {
  id: string;
  documentId: string;
  userId: string;
  userName: string;
  documentType: DocumentType;
  verificationStatus: VerificationStatus;
  verifiedBy: string;
  verifiedByName: string;
  verifiedAt: Date;
  rejectionReason?: string;
}

export interface BulkVerificationDto {
  documentIds: string[];
  status: VerificationStatus;
  rejectionReason?: string;
}

@Injectable()
export class KycAdminService {
  private readonly logger = new Logger(KycAdminService.name);

  constructor(
    @InjectRepository(Document)
    private readonly documentsRepository: Repository<Document>,
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @Inject(DocumentsService)
    private readonly documentsService: DocumentsService,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService,
    @Inject(ActivityLogService)
    private readonly activityLogService: ActivityLogService
  ) {}

  /**
   * Get KYC dashboard statistics
   */
  async getDashboardStats(): Promise<KycDashboardStats> {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const [
      totalPendingDocuments,
      totalVerifiedDocuments,
      totalRejectedDocuments,
      pendingWorkerKyc,
      pendingCompanyKyc,
      verifiedWorkers,
      verifiedCompanies,
      recentSubmissions,
    ] = await Promise.all([
      this.documentsRepository.count({
        where: { verificationStatus: VerificationStatus.PENDING },
      }),
      this.documentsRepository.count({
        where: { verificationStatus: VerificationStatus.VERIFIED },
      }),
      this.documentsRepository.count({
        where: { verificationStatus: VerificationStatus.REJECTED },
      }),
      this.usersRepository.count({
        where: { role: UserRole.WORKER, isKycVerified: false },
      }),
      this.usersRepository.count({
        where: { role: UserRole.COMPANY, isKycVerified: false },
      }),
      this.usersRepository.count({
        where: { role: UserRole.WORKER, isKycVerified: true },
      }),
      this.usersRepository.count({
        where: { role: UserRole.COMPANY, isKycVerified: true },
      }),
      this.documentsRepository.count({
        where: { createdAt: Between(yesterday, now) },
      }),
    ]);

    // Calculate average verification time
    const verifiedDocs = await this.documentsRepository.find({
      where: { verificationStatus: VerificationStatus.VERIFIED },
      select: ["createdAt", "verifiedAt"],
      take: 100, // Sample for performance
    });

    const averageVerificationTime =
      verifiedDocs.length > 0
        ? verifiedDocs.reduce((acc, doc) => {
            if (doc.verifiedAt) {
              const timeDiff =
                doc.verifiedAt.getTime() - doc.createdAt.getTime();
              return acc + timeDiff / (1000 * 60 * 60); // Convert to hours
            }
            return acc;
          }, 0) / verifiedDocs.length
        : 0;

    return {
      totalPendingDocuments,
      totalVerifiedDocuments,
      totalRejectedDocuments,
      pendingWorkerKyc,
      pendingCompanyKyc,
      verifiedWorkers,
      verifiedCompanies,
      recentSubmissions,
      averageVerificationTime: Math.round(averageVerificationTime * 100) / 100,
    };
  }

  /**
   * Get pending KYC documents for review
   */
  async getPendingKycDocuments(
    page: number = 1,
    limit: number = 20,
    priority?: "high" | "medium" | "low",
    documentType?: DocumentType
  ): Promise<{
    items: PendingKycItem[];
    total: number;
    page: number;
    limit: number;
  }> {
    const queryBuilder = this.documentsRepository
      .createQueryBuilder("document")
      .leftJoinAndSelect("document.user", "user")
      .leftJoinAndSelect("document.company", "company")
      .where("document.verificationStatus = :status", {
        status: VerificationStatus.PENDING,
      })
      .orderBy("document.createdAt", "ASC");

    if (documentType) {
      queryBuilder.andWhere("document.documentType = :documentType", {
        documentType,
      });
    }

    const [documents, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    const items: PendingKycItem[] = documents
      .filter((doc) => doc.userId || doc.companyId) // Filter out documents without valid user/company ID
      .map((doc) => {
        // Determine priority based on submission time and user type
        const daysSinceSubmission = Math.floor(
          (Date.now() - doc.createdAt.getTime()) / (1000 * 60 * 60 * 24)
        );

        let priority: "high" | "medium" | "low" = "medium";
        if (daysSinceSubmission > 7) priority = "high";
        else if (daysSinceSubmission > 3) priority = "medium";
        else priority = "low";

        const isCompanyDoc = doc.company !== null;
        const userId = doc.userId || doc.companyId!; // We know one exists due to filter
        const userName = isCompanyDoc ? doc.company?.name : doc.user?.name;
        const userEmail = isCompanyDoc ? doc.company?.email : doc.user?.email;

        return {
          id: doc.id,
          userId,
          userType: isCompanyDoc ? ("company" as const) : ("worker" as const),
          userName: userName || "Unknown",
          userEmail: userEmail || "Unknown",
          documentType: doc.documentType,
          documentUrl: doc.documentUrl,
          documentNumber: doc.documentNumber || undefined,
          submittedAt: doc.createdAt,
          priority,
        };
      });

    // Filter by priority if specified
    const filteredItems = priority
      ? items.filter((item) => item.priority === priority)
      : items;

    return {
      items: filteredItems,
      total,
      page,
      limit,
    };
  }

  /**
   * Get KYC verification history
   */
  async getVerificationHistory(
    page: number = 1,
    limit: number = 20,
    status?: VerificationStatus,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    items: KycVerificationHistory[];
    total: number;
    page: number;
    limit: number;
  }> {
    const queryBuilder = this.documentsRepository
      .createQueryBuilder("document")
      .leftJoinAndSelect("document.user", "user")
      .leftJoinAndSelect("document.company", "company")
      .leftJoinAndSelect("document.verifiedBy", "verifiedBy")
      .where("document.verificationStatus != :pending", {
        pending: VerificationStatus.PENDING,
      })
      .orderBy("document.verifiedAt", "DESC");

    if (status) {
      queryBuilder.andWhere("document.verificationStatus = :status", {
        status,
      });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere(
        "document.verifiedAt BETWEEN :startDate AND :endDate",
        {
          startDate,
          endDate,
        }
      );
    }

    const [documents, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    const items: KycVerificationHistory[] = documents
      .filter((doc) => doc.userId || doc.companyId) // Filter out documents without valid user/company ID
      .map((doc) => ({
        id: doc.id,
        documentId: doc.id,
        userId: doc.userId || doc.companyId!,
        userName: doc.company?.name || doc.user?.name || "Unknown",
        documentType: doc.documentType,
        verificationStatus: doc.verificationStatus,
        verifiedBy: doc.verifiedBy || "Unknown",
        verifiedByName: doc.verifiedBy || "Unknown", // This should be the admin ID, we could fetch admin name if needed
        verifiedAt: doc.verifiedAt!,
        rejectionReason: doc.rejectionReason || undefined,
      }));

    return {
      items,
      total,
      page,
      limit,
    };
  }

  /**
   * Verify a single document
   */
  async verifyDocument(
    documentId: string,
    adminId: string,
    status: VerificationStatus,
    rejectionReason?: string
  ): Promise<Document> {
    const isApproved = status === VerificationStatus.VERIFIED;
    return await this.documentsService.verifyDocument(
      documentId,
      adminId,
      isApproved,
      rejectionReason
    );
  }

  /**
   * Bulk verify multiple documents
   */
  async bulkVerifyDocuments(
    adminId: string,
    bulkVerificationDto: BulkVerificationDto
  ): Promise<{ verified: number; failed: string[] }> {
    const { documentIds, status, rejectionReason } = bulkVerificationDto;
    let verified = 0;
    const failed: string[] = [];

    for (const documentId of documentIds) {
      try {
        await this.verifyDocument(documentId, adminId, status, rejectionReason);
        verified++;
      } catch (error) {
        this.logger.error(
          `Failed to verify document ${documentId}: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
        failed.push(documentId);
      }
    }

    // Log bulk verification activity
    await this.activityLogService.logActivity({
      userId: adminId,
      action: "bulk_verify_documents",
      entityType: "document",
      entityId: "bulk",
      description: `Bulk verified ${verified} documents, ${failed.length} failed`,
    });

    return { verified, failed };
  }

  /**
   * Get document details for review
   */
  async getDocumentForReview(documentId: string): Promise<Document> {
    const document = await this.documentsRepository.findOne({
      where: { id: documentId },
      relations: ["user", "company", "verifiedBy"],
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    return document;
  }

  /**
   * Get user's KYC status and documents
   */
  async getUserKycStatus(userId: string): Promise<{
    user: User;
    documents: Document[];
    kycComplete: boolean;
    missingDocuments: DocumentType[];
  }> {
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      relations: ["documents"],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const documents = user.documents || [];

    // Define required documents based on user role
    const requiredDocuments =
      user.role === UserRole.WORKER
        ? [DocumentType.ID_PROOF, DocumentType.ADDRESS_PROOF]
        : [DocumentType.COMPANY_REGISTRATION, DocumentType.TAX_DOCUMENT];

    const verifiedDocumentTypes = documents
      .filter((doc) => doc.verificationStatus === VerificationStatus.VERIFIED)
      .map((doc) => doc.documentType);

    const missingDocuments = requiredDocuments.filter(
      (type) => !verifiedDocumentTypes.includes(type)
    );

    const kycComplete = missingDocuments.length === 0;

    return {
      user,
      documents,
      kycComplete,
      missingDocuments,
    };
  }

  /**
   * Send reminder to users with pending KYC
   */
  async sendKycReminders(): Promise<{ sent: number; failed: number }> {
    // Get users with incomplete KYC
    const incompleteKycUsers = await this.usersRepository.find({
      where: { isKycVerified: false },
      relations: ["documents"],
    });

    let sent = 0;
    let failed = 0;

    for (const user of incompleteKycUsers) {
      try {
        const requiredDocs =
          user.role === UserRole.WORKER
            ? [DocumentType.ID_PROOF, DocumentType.ADDRESS_PROOF]
            : [DocumentType.COMPANY_REGISTRATION, DocumentType.TAX_DOCUMENT];

        const submittedDocs =
          user.documents?.map((doc) => doc.documentType) || [];
        const missingDocs = requiredDocs.filter(
          (type) => !submittedDocs.includes(type)
        );

        if (missingDocs.length > 0) {
          await this.notificationsService.create({
            userId: user.id,
            title: "Complete Your KYC Verification",
            message: `Please submit your ${missingDocs.join(
              ", "
            )} documents to complete KYC verification`,
            type: "document",
            metadata: { missingDocuments: missingDocs },
            link: "/profile/kyc",
          });
          sent++;
        }
      } catch (error) {
        this.logger.error(
          `Failed to send KYC reminder to user ${user.id}: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
        failed++;
      }
    }

    return { sent, failed };
  }
}
